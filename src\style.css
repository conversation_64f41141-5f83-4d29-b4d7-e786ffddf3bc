@import url('https://fonts.googleapis.com/css2?family=Alexandria:wght@100..900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Prevent horizontal scroll globally */
html, body {
  overflow-x: hidden !important;
  width: 100vw;
  box-sizing: border-box;
}

*, *::before, *::after {
  box-sizing: inherit;
}

/* Remove this block to restore Tailwind's container */
/* Ensure main containers do not overflow */
/*
.container, .carousel-container, .services-section, .features-section, .customers-section, .templates-grid, .blog-section, .contact-section, .achievement-item, .scroll-works-section {
  max-width: 100vw;
  overflow-x: hidden;
}
*/

/* Burger Menu Styles */
.burger-menu-btn {
  z-index: 60;
}

.burger-line {
  transform-origin: center;
  will-change: transform, opacity;
}

.mobile-menu {
  box-shadow: -5px 0 25px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  will-change: transform;
}

[dir="rtl"] .mobile-menu {
  box-shadow: 5px 0 25px rgba(0, 0, 0, 0.3);
}

.mobile-nav-item {
  will-change: transform, opacity;
}

.mobile-nav-item a {
  position: relative;
  overflow: hidden;
}

.mobile-nav-item a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(222, 133, 69, 0.1), transparent);
  transition: width 0.3s ease;
}

[dir="rtl"] .mobile-nav-item a::before {
  left: auto;
  right: 0;
}

.mobile-nav-item a:hover::before {
  width: 100%;
}

.mobile-menu-actions {
  will-change: transform, opacity;
}

/* Services Grid Container Styles */
.services-grid-container {
  position: relative;
  width: 100%;
  max-width: 100%;
  aspect-ratio: 4/3;
  margin: 0 auto;
}

@media (min-width: 640px) {
  .services-grid-container {
    max-width: 500px;
    aspect-ratio: 5/4;
  }
}

@media (min-width: 768px) {
  .services-grid-container {
    max-width: 600px;
    aspect-ratio: 3/2;
  }
}

@media (min-width: 1024px) {
  .services-grid-container {
    max-width: 700px;
    aspect-ratio: 16/11;
  }
}

/* Grid items positioning using percentages */
.services-grid .grid-item {
  position: absolute;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.services-grid .grid-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.services-grid .grid-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.services-grid .grid-item:hover img {
  transform: scale(1.05);
}

/* Mobile Menu Responsive Styles */
@media (max-width: 1024px) {
  #mobileMenu,
  #mobileMenuOverlay {
    max-width: 100vw;
    overflow-x: hidden;
  }

  .mobile-menu {
    max-width: min(85vw, 320px);
  }
}

@media (max-width: 640px) {
  .mobile-menu {
    max-width: min(90vw, 280px);
  }

  .mobile-nav-item a {
    padding: 0.75rem;
    font-size: 0.95rem;
  }

  .mobile-menu-actions button {
    padding: 0.875rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .mobile-menu {
    max-width: 95vw;
  }

  .mobile-nav-item a {
    padding: 0.625rem;
    font-size: 0.9rem;
  }

  .mobile-menu-actions button {
    padding: 0.75rem;
    font-size: 0.85rem;
  }

  .mobile-menu nav {
    padding: 1.25rem;
  }

  .mobile-menu .flex.items-center.justify-between {
    padding: 1.25rem;
  }
}

/* Fix for images and grids */
img, svg, video {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Remove negative margins that may cause overflow */
[class*="-mt-"] {
  margin-top: 0 !important;
}

/* Remove horizontal padding on small screens for .container */
/* Services Grid Item Positioning */
/* Item 1 - Top left */
.services-grid .grid-item-1 {
  top: 8%;
  left: 5%;
  width: 32%;
  height: 25%;
}

/* Item 2 - Top center */
.services-grid .grid-item-2 {
  top: 3%;
  left: 42%;
  width: 25%;
  height: 25%;
}

/* Item 3 - Center left */
.services-grid .grid-item-3 {
  top: 42%;
  left: 8%;
  width: 22%;
  height: 20%;
}

/* Item 4 - Center center (main focus) */
.services-grid .grid-item-4 {
  top: 25%;
  left: 35%;
  width: 30%;
  height: 35%;
}

/* Item 5 - Bottom center */
.services-grid .grid-item-5 {
  top: 68%;
  left: 18%;
  width: 38%;
  height: 20%;
}

/* Item 6 - Center right */
.services-grid .grid-item-6 {
  top: 22%;
  left: 68%;
  width: 28%;
  height: 15%;
}

/* Item 7 - Bottom right */
.services-grid .grid-item-7 {
  top: 45%;
  left: 68%;
  width: 28%;
  height: 32%;
}

/* Mobile adjustments for grid items */
@media (max-width: 639px) {
  .services-grid .grid-item-1 {
    top: 10%;
    left: 3%;
    width: 35%;
    height: 22%;
  }

  .services-grid .grid-item-2 {
    top: 5%;
    left: 45%;
    width: 28%;
    height: 22%;
  }

  .services-grid .grid-item-3 {
    top: 45%;
    left: 5%;
    width: 25%;
    height: 18%;
  }

  .services-grid .grid-item-4 {
    top: 28%;
    left: 32%;
    width: 35%;
    height: 30%;
  }

  .services-grid .grid-item-5 {
    top: 72%;
    left: 15%;
    width: 42%;
    height: 18%;
  }

  .services-grid .grid-item-6 {
    top: 25%;
    left: 70%;
    width: 25%;
    height: 14%;
  }

  .services-grid .grid-item-7 {
    top: 48%;
    left: 70%;
    width: 25%;
    height: 28%;
  }
}

/* Service items hover effects */
.service-item {
  transition: all 0.3s ease;
  cursor: pointer;
}

.service-item:hover {
  transform: translateY(-2px);
}

/* Active state for service items */
.service-item.active {
  border-color: var(--primary-blue, #15205C) !important;
  background-color: #EFF6FF;
}

.services-grid .grid-item.active {
  transform: translateY(-4px) scale(1.02) !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

/* Responsive text sizing */
@media (max-width: 639px) {
  .service-title {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .service-description {
    font-size: 0.75rem;
    line-height: 1rem;
  }
}


/* RTL Testimonials Carousel Styles */
.testimonials-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.testimonial-card {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.testimonial-card.active {
  opacity: 1;
  transform: scale(1);
  z-index: 3;
}

.testimonial-card.next {
  opacity: 0.8;
  transform: scale(0.8);
  z-index: 2;
}

.testimonial-card.further {
  opacity: 0.6;
  transform: scale(0.6);
  z-index: 1;
}

.testimonial-card.hidden {
  opacity: 0;
  transform: scale(0.4);
  z-index: 0;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

.float-animation:nth-child(2) {
  animation-delay: 0.5s;
}

.float-animation:nth-child(3) {
  animation-delay: 1s;
}

.content-fade-in {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav-btn {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(36, 88, 167, 0.3);
}

.star-rating {
  display: flex;
  flex-direction: row;
  gap: 4px;
}

/* Testimonial Images Row */
.testimonial-images-row {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

.testimonial-image-wrapper {
  position: relative;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
  border-radius: 0.75rem;
}

.testimonial-image-wrapper img {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border-radius: 0.75rem;
  filter: brightness(0.95);
}

.testimonial-image-wrapper:hover img {
  box-shadow: 0 25px 50px rgba(36, 88, 167, 0.4);
  transform: translateY(-8px) scale(1.02);
  filter: brightness(1);
}

/* Image overlay styles */
.image-overlay {
  position: absolute;
  bottom: 0.75rem;
  left: 0.75rem;
  right: 0.75rem;
  background: rgba(255, 255, 255, 0.95);
  padding: 0.75rem;
  border-radius: 0.5rem;
  backdrop-filter: blur(15px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(10px);
  opacity: 0;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.testimonial-image-wrapper:hover .image-overlay {
  background: rgba(255, 255, 255, 1);
  transform: translateY(0);
  opacity: 1;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Fade effects for different image states */
.testimonial-image-wrapper.active {
  animation: pulseActive 2s ease-in-out infinite;
}

.testimonial-image-wrapper.active img {
  opacity: 1;
  transform: scale(1);
  filter: brightness(1);
  box-shadow: 0 20px 40px rgba(36, 88, 167, 0.3);
}

.testimonial-image-wrapper.active .image-overlay {
  opacity: 1;
  transform: translateY(0);
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.testimonial-image-wrapper.next img {
  opacity: 0.85;
  transform: scale(0.85);
  filter: brightness(0.9);
}

.testimonial-image-wrapper.further img {
  opacity: 0.65;
  transform: scale(0.7);
  filter: brightness(0.8);
}

@keyframes pulseActive {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

/* Content Animations */
.testimonial-content {
  animation: fadeInLeft 0.8s ease-out;
}

.testimonial-title {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.testimonial-rating {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.testimonial-text {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.testimonial-customer-info {
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

.testimonial-nav {
  animation: fadeInUp 0.8s ease-out 1s both;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Embla Dots */
.embla__dots {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 1.5rem;
  animation: fadeInUp 0.8s ease-out 1.2s both;
}

.embla__dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: #d1d5db;
  border: none;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.embla__dot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: #2458A7;
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translate(-50%, -50%);
}

.embla__dot:hover::before {
  width: 100%;
  height: 100%;
}

.embla__dot:hover {
  transform: scale(1.3);
  box-shadow: 0 4px 15px rgba(36, 88, 167, 0.3);
}

.embla__dot.bg-primary-blue {
  background-color: #2458A7;
  transform: scale(1.4);
  box-shadow: 0 6px 20px rgba(36, 88, 167, 0.4);
}

/* Navigation Buttons */
.testimonial-nav .nav-btn {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(36, 88, 167, 0.2);
  position: relative;
  overflow: hidden;
}

.testimonial-nav .nav-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translate(-50%, -50%);
}

.testimonial-nav .nav-btn:hover::before {
  width: 200%;
  height: 200%;
}

.testimonial-nav .nav-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 30px rgba(36, 88, 167, 0.4);
}

.testimonial-nav .nav-btn:active {
  transform: translateY(-1px) scale(1.02);
}

/* Star Rating Animation */
.testimonial-rating i {
  animation: starTwinkle 2s ease-in-out infinite;
  animation-delay: calc(var(--star-index, 0) * 0.2s);
}

@keyframes starTwinkle {
  0%, 100% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.1);
    filter: brightness(1.2);
  }
}

/* Responsive Design */

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
  .testimonial-images-row {
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .testimonial-image-wrapper.active img {
    width: 220px;
    height: 300px;
  }

  .testimonial-image-wrapper.next img {
    width: 176px;
    height: 240px;
  }

  .testimonial-image-wrapper.further img {
    width: 132px;
    height: 180px;
  }
}

/* Desktop (1024px - 1439px) */
@media (max-width: 1439px) and (min-width: 1024px) {
  .testimonial-images-row {
    gap: 1.25rem;
    padding: 1.25rem;
  }

  .testimonial-image-wrapper.active img {
    width: 200px;
    height: 280px;
  }

  .testimonial-image-wrapper.next img {
    width: 160px;
    height: 224px;
  }

  .testimonial-image-wrapper.further img {
    width: 120px;
    height: 168px;
  }
}

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .testimonials-section {
    padding: 2rem 1rem;
  }

  .testimonial-images-row {
    gap: 1rem;
    height: 280px;
    padding: 1rem;
  }

  .testimonial-image-wrapper.active img {
    width: 160px;
    height: 220px;
  }

  .testimonial-image-wrapper.next img {
    width: 128px;
    height: 176px;
  }

  .testimonial-image-wrapper.further img {
    width: 96px;
    height: 132px;
  }

  .testimonial-title {
    font-size: 1.75rem;
  }

  .testimonial-text p {
    font-size: 1rem;
  }
}

/* Mobile Large (640px - 767px) */
@media (max-width: 767px) and (min-width: 640px) {
  .testimonials-section {
    padding: 1.5rem 0.75rem;
  }

  .testimonial-images-row {
    gap: 0.75rem;
    height: 240px;
    padding: 0.75rem;
  }

  .testimonial-image-wrapper.active img {
    width: 140px;
    height: 200px;
  }

  .testimonial-image-wrapper.next img {
    width: 112px;
    height: 160px;
  }

  .testimonial-image-wrapper.further img {
    width: 84px;
    height: 120px;
  }

  .testimonial-title {
    font-size: 1.5rem;
    line-height: 1.3;
  }

  .testimonial-text p {
    font-size: 0.95rem;
    line-height: 1.6;
  }

  .embla__dot {
    width: 0.625rem;
    height: 0.625rem;
  }

  .image-overlay {
    padding: 0.5rem;
    bottom: 0.5rem;
    left: 0.5rem;
    right: 0.5rem;
  }

  .image-overlay p {
    font-size: 0.8rem;
  }
}

/* Mobile Medium (480px - 639px) */
@media (max-width: 639px) and (min-width: 480px) {
  .testimonials-section {
    padding: 1.25rem 0.5rem;
  }

  .testimonial-images-row {
    gap: 0.5rem;
    height: 200px;
    padding: 0.5rem;
  }

  .testimonial-image-wrapper.active img {
    width: 120px;
    height: 170px;
  }

  .testimonial-image-wrapper.next img {
    width: 96px;
    height: 136px;
  }

  .testimonial-image-wrapper.further img {
    width: 72px;
    height: 102px;
  }

  .testimonial-title {
    font-size: 1.375rem;
    line-height: 1.25;
  }

  .testimonial-text p {
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .testimonial-nav .nav-btn {
    width: 2.25rem;
    height: 2.25rem;
  }

  .embla__dot {
    width: 0.5rem;
    height: 0.5rem;
  }

  .image-overlay {
    padding: 0.375rem;
    bottom: 0.375rem;
    left: 0.375rem;
    right: 0.375rem;
  }

  .image-overlay p {
    font-size: 0.75rem;
  }
}

/* Works Filter Styles */
.work-tab {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.work-tab::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translate(-50%, -50%);
}

.work-tab:hover::before {
  width: 200%;
  height: 200%;
}

.work-tab.active {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(36, 88, 167, 0.3);
}

.works-down-arrow {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.works-down-arrow::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translate(-50%, -50%);
}

.works-down-arrow:hover::before {
  width: 200%;
  height: 200%;
}

.works-down-arrow:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 30px rgba(36, 88, 167, 0.4);
}

.works-down-arrow:active {
  transform: translateY(-1px) scale(0.98);
}

/* Responsive Works Filter */
@media (max-width: 768px) {
  .work-tab {
    padding: 0.5rem 1rem !important;
    font-size: 0.75rem;
  }

  .works-down-arrow {
    width: 2.5rem;
    height: 2.5rem;
  }

  .works-down-arrow i {
    font-size: 0.875rem;
  }
}

@media (max-width: 640px) {
  .work-tab {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.7rem;
  }

  .works-down-arrow {
    width: 2rem;
    height: 2rem;
  }

  .works-down-arrow i {
    font-size: 0.75rem;
  }
}

/* Mobile Small (320px - 479px) */
@media (max-width: 479px) {
  .testimonials-section {
    padding: 1rem 0.25rem;
  }

  .testimonial-images-row {
    gap: 0.375rem;
    height: 180px;
    padding: 0.375rem;
    flex-direction: row;
    justify-content: space-around;
  }

  .testimonial-image-wrapper.active img {
    width: 100px;
    height: 140px;
  }

  .testimonial-image-wrapper.next img {
    width: 80px;
    height: 112px;
  }

  .testimonial-image-wrapper.further img {
    width: 60px;
    height: 84px;
  }

  .testimonial-title {
    font-size: 1.25rem;
    line-height: 1.2;
    margin-bottom: 0.75rem;
  }

  .testimonial-text p {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .testimonial-nav .nav-btn {
    width: 2rem;
    height: 2rem;
    font-size: 0.75rem;
  }

  .embla__dot {
    width: 0.375rem;
    height: 0.375rem;
  }

  .image-overlay {
    display: none; /* Hide overlays on very small screens */
  }

  /* Stack content vertically on very small screens */
  .grid.grid-cols-1.lg\\:grid-cols-2 {
    gap: 1rem;
  }

  .testimonial-content {
    order: 2;
    text-align: center;
  }

  .testimonial-carousel {
    order: 1;
  }
}

/* Animation Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .testimonial-image-wrapper img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Templates Section Styles */
.template-tab {
  position: relative;
  cursor: pointer;
}

.template-tab.active {
  color: #2458A7;
  border-bottom-color: #F4B942;
}

.template-tab:hover:not(.active) {
  color: #2458A7;
  border-bottom-color: #F4B942;
}

.template-card {
  cursor: pointer;
}

.template-image {
  overflow: hidden;
}



.btn-preview,
.btn-purchase {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-preview:hover {
  background-color: #e5e7eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-purchase:hover {
  background-color: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(36, 88, 167, 0.3);
}

.view-all-btn {
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(36, 88, 167, 0.2);
}

.view-all-btn:hover {
  background-color: #1d4ed8;
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(36, 88, 167, 0.3);
}

/* Responsive adjustments for templates */
@media (max-width: 768px) {
  .template-card {
    margin-bottom: 1.5rem;
  }

  .template-tech {
    gap: 0.5rem;
  }

  .tech-tag {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  .template-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn-preview,
  .btn-purchase {
    width: 100%;
    text-align: center;
  }
}

* {
  font-family: "Alexandria", sans-serif;
  line-height: 2;
}

/* Accordion Styles */
.accordion-container {
  align-items: start !important;
}

.accordion-item {
  align-self: start;
  height: fit-content;
  min-height: auto;
}

@media (min-width: 1024px) {
  .accordion-container {
    grid-auto-rows: max-content;
  }

  .accordion-item {
    height: auto;
    align-self: start;
  }
}

/* Custom AOS animations */
[data-aos="fade-in-rotate-x"] {
  opacity: 0;
  transform: rotateX(180deg);
  transition-property: opacity, transform;
}

[data-aos="fade-in-rotate-x"].aos-animate {
  opacity: 1;
  transform: rotateX(0deg);
}

/* Floating animations that combine with scale animations */
@keyframes float-hand-1 {
  0%, 30% {
    transform: translate(0px, 0px) scale(1);
  }
  25% {
    transform: translate(15px, -10px) scale(1.2);
  }
  50% {
    transform: translate(-10px, -20px) scale(1.8);
  }
  70% {
    transform: translate(-15px, 10px) scale(1.2);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes float-winning-2 {
  0%, 30% {
    transform: translate(0px, 0px) scale(1);
  }
  25% {
    transform: translate(-12px, 15px) scale(1.2);
  }
  50% {
    transform: translate(18px, -8px) scale(1.8);
  }
  70% {
    transform: translate(8px, -18px) scale(1.2);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes float-hand-3 {
  0%, 30% {
    transform: translate(0px, 0px) scale(1);
  }
  25% {
    transform: translate(10px, 12px) scale(1.2);
  }
  50% {
    transform: translate(-15px, 5px) scale(1.8);
  }
  70% {
    transform: translate(12px, -15px) scale(1.2);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes float-winning-4 {
  0%, 30% {
    transform: translate(0px, 0px) scale(1);
  }
  25% {
    transform: translate(-8px, -12px) scale(1.2);
  }
  50% {
    transform: translate(14px, 10px) scale(1.8);
  }
  70% {
    transform: translate(-10px, 8px) scale(1.2);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.float-hand-1 {
  animation: float-hand-1 6s ease-in-out infinite;
}

.float-winning-2 {
  animation: float-winning-2 7s ease-in-out infinite;
}

.float-hand-3 {
  animation: float-hand-3 8s ease-in-out infinite;
}

.float-winning-4 {
  animation: float-winning-4 5s ease-in-out infinite;
}

/* Simple floating animations for flying icons */
@keyframes simple-float-1 {
  0%, 100% {
    transform: translate(0px, 0px);
  }
  25% {
    transform: translate(15px, -10px);
  }
  50% {
    transform: translate(-10px, -20px);
  }
  75% {
    transform: translate(-15px, 10px);
  }
}

@keyframes simple-float-2 {
  0%, 100% {
    transform: translate(0px, 0px);
  }
  25% {
    transform: translate(-12px, 15px);
  }
  50% {
    transform: translate(18px, -8px);
  }
  75% {
    transform: translate(8px, -18px);
  }
}

@keyframes simple-float-3 {
  0%, 100% {
    transform: translate(0px, 0px);
  }
  25% {
    transform: translate(10px, 12px);
  }
  50% {
    transform: translate(-15px, 5px);
  }
  75% {
    transform: translate(12px, -15px);
  }
}

@keyframes simple-float-4 {
  0%, 100% {
    transform: translate(0px, 0px);
  }
  25% {
    transform: translate(-8px, -12px);
  }
  50% {
    transform: translate(14px, 10px);
  }
  75% {
    transform: translate(-10px, 8px);
  }
}

.float-1 {
  animation: simple-float-1 6s ease-in-out infinite;
}

.float-2 {
  animation: simple-float-2 7s ease-in-out infinite;
}

.float-3 {
  animation: simple-float-3 8s ease-in-out infinite;
}

.float-4 {
  animation: simple-float-4 5s ease-in-out infinite;
}

/* Blur effect for smooth image transitions */
.blur-out {
  filter: blur(15px);
  opacity: 0.2;
  transition: filter 1.5s ease-in-out, opacity 1.5s ease-in-out;
}

/* Grid images cropping effect - show only portions like background-size: 400% 300% */
/* .grid-item img {
  width: 400% !important;
  height: 300% !important;
  object-fit: cover;
} */

/* Hide scrollbar for projects section while keeping scroll functionality */
.container.h-\[945px\] {
  /* Hide scrollbar for Chrome, Safari and Opera */
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.container.h-\[945px\]::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Responsive scroll works card sizing */
.scroll-work-card {
  transition: all 0.3s ease;
}

.scroll-work-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Mobile screens */
@media (max-width: 640px) {
  .scroll-work-card {
    width: 85% !important;
    height: 400px !important;
    left: 7.5% !important;
  }
}

/* Tablet screens */
@media (min-width: 641px) and (max-width: 1024px) {
  .scroll-work-card {
    width: 75% !important;
    height: 450px !important;
    left: 12.5% !important;
  }
}

/* Desktop screens */
@media (min-width: 1025px) {
  .scroll-work-card {
    width: 45% !important;
    height: 500px !important;
  }

  .scroll-work-card[data-side="left"] {
    left: 5% !important;
  }

  .scroll-work-card[data-side="right"] {
    left: 50% !important;
  }
}

/* Rectangle flip for achievement section */
.rectangle-flip {
  transform: scaleY(-1) !important;
}

/* Templates Carousel Specific Styles */
.templates-carousel-container {
  width: 100%;
}

.templates-carousel-container .embla__viewport {
  overflow: hidden;
}

.templates-carousel-container .embla__container {
  display: flex;
  gap: 2rem;
}

.templates-carousel-container .embla__slide {
  flex: 0 0 auto;
  min-width: 0;
  width: 100%;
}

@media (min-width: 768px) {
  .templates-carousel-container .embla__slide {
    width: calc(50% - 1rem);
  }
}

@media (min-width: 1024px) {
  .templates-carousel-container .embla__slide {
    width: calc(33.333% - 1.333rem);
  }
}

/* Blogs Carousel Specific Styles */
.blogs-carousel-container {
  width: 100%;
}

.blogs-carousel-container .embla__viewport {
  overflow: hidden;
}

.blogs-carousel-container .embla__container {
  display: flex;
  gap: 0;
}

.blogs-carousel-container .embla__slide {
  flex: 0 0 auto;
  min-width: 0;
  width: 100%;
}

@media (min-width: 640px) {
  .blogs-carousel-container .embla__slide {
    width: 50%;
  }
}

@media (min-width: 1024px) {
  .blogs-carousel-container .embla__slide {
    width: 33.333%;
  }
}

@media (min-width: 1280px) {
  .blogs-carousel-container .embla__slide {
    width: 25%;
  }
}

/* Blogs carousel dots */
.blogs-carousel-container .embla__dots {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 1.5rem;
  animation: fadeInUp 0.8s ease-out 1.2s both;
}

.blogs-carousel-container .embla__dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: #d1d5db;
  border: none;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.blogs-carousel-container .embla__dot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: #2458A7;
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translate(-50%, -50%);
}

.blogs-carousel-container .embla__dot:hover::before,
.blogs-carousel-container .embla__dot--selected::before {
  width: 100%;
  height: 100%;
}

.blogs-carousel-container .embla__dot:hover,
.blogs-carousel-container .embla__dot--selected {
  transform: scale(1.3);
  box-shadow: 0 4px 15px rgba(36, 88, 167, 0.3);
}

.blogs-carousel-container .embla__dot--selected {
  background-color: #2458A7;
}

/* Laptop Container Styles */
.laptop-container {
  position: relative;
  display: inline-block;
}

.laptop-screen-overlay {
  position: absolute;
  /* These percentages are based on the laptop image proportions */
  top: 8.5%;
  left: 50%;
  transform: translateX(-50%);
  width: 68.5%;
  height: 75%;
  overflow: hidden;
  border-radius: 2px;
}

.laptop-website {
  width: 100%;
  height: auto;
  min-height: 100%;
  object-fit: cover;
  object-position: top;
  transition: transform 3s ease-in-out;
}

.laptop-website:hover {
  transform: translateY(-70%);
}

/* Responsive adjustments if needed */
@media (max-width: 768px) {
  .laptop-screen-overlay {
    top: 8%;
    width: 67%;
    height: 76%;
  }
}

